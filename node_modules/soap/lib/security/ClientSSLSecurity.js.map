{"version": 3, "file": "ClientSSLSecurity.js", "sourceRoot": "", "sources": ["../../src/security/ClientSSLSecurity.ts"], "names": [], "mappings": ";;;AAAA,uBAAyB;AACzB,6BAA+B;AAC/B,0BAA4B;AAG5B;;;;;;;;;GASG;AACH;IAOE,2BAAY,GAAoB,EAAE,IAAqB,EAAE,EAAkC,EAAE,QAAc;QACzG,IAAI,GAAG,EAAE;YACP,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;gBACxB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;aAChB;iBAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;gBAClC,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;aACjC;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;aACxD;SACF;QAED,IAAI,IAAI,EAAE;YACR,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;gBACzB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;aAClB;iBAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBACnC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;aACnC;iBAAM;gBACL,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;aACzD;SACF;QAED,IAAI,EAAE,EAAE;YACN,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBAC5C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;aACd;iBAAM,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;gBACjC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;aAC/B;iBAAM;gBACL,QAAQ,GAAG,EAAE,CAAC;gBACd,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;aAChB;SACF;QAED,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAEjC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IACpB,CAAC;IAEM,iCAAK,GAAZ;QACE,OAAO,EAAE,CAAC;IACZ,CAAC;IAEM,sCAAU,GAAjB,UAAkB,OAAY;QAC5B,IAAI,UAAU,GAAG,IAAI,CAAC;QAEtB,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;QACvB,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACzB,OAAO,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACrB,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;gBACf,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;gBAEzB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;aACvC;YAED,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;SACzB;aAAM;YACL,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACvC;QAED,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;IAClC,CAAC;IACH,wBAAC;AAAD,CAAC,AAvED,IAuEC;AAvEY,8CAAiB"}