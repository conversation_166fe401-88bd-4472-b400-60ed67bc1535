{"version": 3, "file": "WSSecurityCert.js", "sourceRoot": "", "sources": ["../../src/security/WSSecurityCert.ts"], "names": [], "mappings": ";;;AAAA,6BAAoC;AACpC,yCAAuC;AAGvC,SAAS,UAAU,CAAC,IAAU,EAAE,OAAe;IAC7C,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAU;IACnC,OAAO,IAAI,CAAC,cAAc,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACnF,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;QACtF,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAChG,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC;AACvC,CAAC;AAED,SAAS,eAAe;IACtB,OAAO,iBAAiB,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,SAAS,CAAC,GAAW,EAAE,GAAW,EAAE,GAAW;IACtD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,UAAU;IACjB,OAAO,SAAM,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,8BAA8B,CAAC,UAAiB,EAAE,SAAiB;IAC1E,KAAkB,UAAU,EAAV,yBAAU,EAAV,wBAAU,EAAV,IAAU,EAAE;QAAzB,IAAM,GAAG,mBAAA;QACZ,IAAI,GAAG,CAAC,KAAK,KAAK,oBAAoB,EAAE;YACtC,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;SACvB;KACF;AACH,CAAC;AAED,IAAM,YAAY,GAAG,wCAAwC,CAAC;AAC9D,IAAM,oBAAoB,GAAG,eAAe,CAAC;AAgB7C;IAWE,wBAAY,UAAe,EAAE,YAAiB,EAAE,QAAa,EAAE,OAAoC;QAAnG,iBAgDC;QAhD8D,wBAAA,EAAA,YAAoC;QAR3F,kBAAa,GAAsB,EAAE,CAAC;QAMtC,yBAAoB,GAAa,EAAE,CAAC;QAG1C,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,QAAQ,EAAE;aACxC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;aAC1C,OAAO,CAAC,2BAA2B,EAAE,EAAE,CAAC;aACxC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAEjC,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAS,EAAE,CAAC;QAC9B,IAAI,OAAO,CAAC,kBAAkB,KAAK,mDAAmD,EAAE;YACtF,IAAI,CAAC,MAAM,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,oBAAoB,EACpB,CAAC,yCAAyC,CAAC,EAC3C,yCAAyC,CAC1C,CAAC;SACH;QAED,IAAI,OAAO,CAAC,oBAAoB,IAAI,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3E,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;SAC1D;QAED,IAAI,OAAO,CAAC,aAAa,EAAE;YACjB,IAAA,aAAa,GAAK,OAAO,cAAZ,CAAa;YAClC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;YACnC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE;gBACxC,IAAI,CAAC,aAAa,CAAC,gBAAgB,GAAG,EAAE,CAAC;aAC1C;YACD,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,EAAE;gBACpF,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,IAAI,GAAM,YAAY,gDAA6C,CAAC;aACzG;SACF;aAAM;YACL,IAAI,CAAC,aAAa,GAAG,EAAE,gBAAgB,EAAE,EAAE,IAAI,EAAK,YAAY,gDAA6C,EAAE,EAAE,CAAC;SACnH;QAED,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG;YACvB,GAAG,EAAE,UAAU;YACf,UAAU,EAAE,QAAQ;SACrB,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,UAAQ,UAAU,EAAI,CAAC;QACrC,IAAI,CAAC,YAAY,GAAG,OAAO,OAAO,CAAC,YAAY,KAAK,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QAChG,IAAI,CAAC,wBAAwB,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,wBAAwB;YAChH,CAAC,CAAC,CAAC,uDAAuD,EAAE,yCAAyC,CAAC,CAAC;QAEzG,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,UAAU,GAAG,UAAC,GAAG;YAC3C,OAAO,+BAA+B;iBACpC,4BAAyB,KAAI,CAAC,MAAM,uBAAgB,YAAY,wDAAoD,CAAA;gBACpH,gCAAgC,CAAC;QACrC,CAAC,CAAC;IACJ,CAAC;IAEM,oCAAW,GAAlB,UAAmB,GAAG,EAAE,WAAW;QACjC,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,eAAe,EAAE,CAAC;QAEjC,IAAI,YAAY,GAAG,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,YAAY;gBACV,wBAAqB,YAAY,8DAAwD;qBACzF,cAAY,IAAI,CAAC,OAAO,eAAY,CAAA;qBACpC,cAAY,IAAI,CAAC,OAAO,eAAY,CAAA;oBACpC,cAAc,CAAC;SAClB;QAED,IAAM,SAAS,GACb,iCAA8B,YAAY,mDAA+C;aACzF,iBAAc,YAAY,oDAAgD,CAAA;aACvE,WAAW,2BAAsB,CAAA;YACpC,4BAA4B;aAC5B,oBAAiB,YAAY,gEAA4D,CAAA;aACzF,iBAAc,YAAY,uDAAmD,CAAA;aAC7E,cAAW,IAAI,CAAC,MAAM,WAAK,IAAI,CAAC,YAAY,gCAA6B,CAAA;YACzE,YAAY;YACZ,kBAAkB,CAAC;QAErB,IAAM,UAAU,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,OAAK,WAAW,aAAU,CAAC,CAAC,CAAC;QAEtF,IAAM,UAAU,GAAG,IAAI,CAAC,wBAAwB,CAAC;QAEjD,IAAM,SAAS,GAAG,kBAAgB,WAAW,YAAS,CAAC;QACvD,8BAA8B,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;QAElE,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,GAAG,CAAC,KAAK,KAAK,SAAS,CAAC,EAAzB,CAAyB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YACnF,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;SACjD;gCAEU,MAAI;YACb,IAAM,KAAK,GAAG,kBAAgB,MAAI,OAAI,CAAC;YACvC,IAAI,CAAC,CAAC,OAAK,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,EAArB,CAAqB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;gBAC/E,OAAK,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;aAC7C;;;QAJH,KAAmB,UAAyB,EAAzB,KAAA,IAAI,CAAC,oBAAoB,EAAzB,cAAyB,EAAzB,IAAyB;YAAvC,IAAM,MAAI,SAAA;oBAAJ,MAAI;SAKd;QAED,IAAM,cAAc,GAAG,2DAA2D,CAAC;QACnF,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAC,GAAG,IAAK,OAAA,CAAC,GAAG,CAAC,KAAK,KAAK,cAAc,CAAC,EAA9B,CAA8B,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC7G,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAE7D,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;IACtG,CAAC;IACH,qBAAC;AAAD,CAAC,AAhHD,IAgHC;AAhHY,wCAAc"}