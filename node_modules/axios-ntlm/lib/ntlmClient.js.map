{"version": 3, "file": "ntlmClient.js", "sourceRoot": "", "sources": ["../src/ntlmClient.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA4F;AAMnF,2FANO,kBAAU,OAMP;AALnB,2CAA+B;AAC/B,2CAA+B;AAC/B,yCAA6B;AAC7B,sDAA+B;AAiB/B;;;;;EAKE;AACF,SAAgB,UAAU,CAAC,WAA4B,EAAE,WAAgC;IAAzF,iBAiEC;IAhEG,IAAI,MAAM,GAAuB,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,EAAE,CAAA;IAElD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;QACnB,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAC1D;IAED,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;QACpB,MAAM,CAAC,UAAU,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAC5D;IAED,IAAM,MAAM,GAAG,eAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAEpC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAC,QAAQ;QACtC,OAAO,QAAQ,CAAC;IACpB,CAAC,EAAE,UAAO,GAAyB;;;;;;oBACzB,KAAK,GAA8B,GAAG,CAAC,QAAQ,CAAC;yBAElD,CAAA,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,GAAG;2BAC1B,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC;2BACjC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;2BAClD,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,OAAO,CAAC,CAAA,EAHpF,wBAGoF;oBAI9E,UAAU,GAAG,CAAA,MAAA,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAC,MAAc,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAtB,CAAsB,CAAC,0CAAE,IAAI,EAAE,KAAI,EAAE,CAAC;oBAE/H,qEAAqE;oBACrE,mEAAmE;oBACnE,mEAAmE;oBACnE,yCAAyC;oBACzC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE;wBAAE,KAAK,CAAC,MAAM,CAAC,OAAO,GAAQ,EAAE,CAAA;qBAAE;oBAC7D,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE;wBAClB,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,WAAY,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;wBAEpF,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;qBAEjD;yBAAM;wBACG,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAEvF,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,WAAY,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;wBAEvI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;wBAC1C,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,KAAK,CAAC;qBACjD;yBAEG,CAAA,KAAK,CAAC,MAAM,CAAC,YAAY,KAAK,QAAQ,CAAA,EAAtC,wBAAsC;oBAChC,WAA2C,MAAA,GAAG,CAAC,QAAQ,0CAAE,IAAI,CAAC;yBAGhE,CAAA,QAAM,IAAI,CAAC,QAAM,CAAC,aAAa,CAAA,EAA/B,wBAA+B;oBAC/B,qBAAM,IAAI,OAAO,CAAO,UAAA,OAAO;4BAC3B,QAAM,CAAC,IAAI,CAAC,IAAA,kBAAO,GAAE,CAAC,CAAC;4BACvB,QAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;wBAClC,CAAC,CAAC,EAAA;;oBAHF,SAGE,CAAC;;wBAIX,sBAAO,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAC;wBAE5B,MAAM,GAAG,CAAC;;;SAEjB,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAClB,CAAC;AAjED,gCAiEC"}