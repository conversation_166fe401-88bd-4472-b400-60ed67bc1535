{"version": 3, "file": "ntlm.js", "sourceRoot": "", "sources": ["../src/ntlm.js"], "names": [], "mappings": "AAAA,YAAY,CAAC;AACb,gGAAgG;AAEhG,IAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,EACvB,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,EAC1B,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AAE1B,IAAM,aAAa,GAAG,WAAW,CAAC;AAElC,SAAS,kBAAkB,CAAC,WAAW,EAAE,MAAM;IAC9C,IAAI,OAAO,GAAG,EAAE,EACf,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9B,WAAW,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;IACtE,MAAM,GAAG,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;IAE5C,WAAW;IACX,GAAG,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAC7D,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC;IAE5B,cAAc;IACd,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;IAC1B,GAAG,IAAI,CAAC,CAAC;IAET,OAAO;IACP,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,sBAAsB;QACzC,KAAK,CAAC,uBAAuB;QAC7B,KAAK,CAAC,2BAA2B;QACjC,KAAK,CAAC,4BAA4B;QAClC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;IAChD,GAAG,IAAI,CAAC,CAAC;IAET,wBAAwB;IACxB,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACtC,GAAG,IAAI,CAAC,CAAC;IACT,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACtC,GAAG,IAAI,CAAC,CAAC;IACT,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAC1D,GAAG,IAAI,CAAC,CAAC;IAET,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KAC/C;IAED,6BAA6B;IAC7B,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3C,GAAG,IAAI,CAAC,CAAC;IACT,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC3C,GAAG,IAAI,CAAC,CAAC;IACT,GAAG,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAC/D,GAAG,IAAI,CAAC,CAAC;IAET,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;QAC3B,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;KACpD;IAED,OAAO,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAG;IAC9B,IAAI,GAAG,KAAK,SAAS,EAAE;QACtB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KACpC;IAED,aAAa;IACb,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,iBAAiB,EAAE;QAC9D,IAAI,GAAG,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAE;YACpF,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;SACtC;aAAM;YACN,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACpC;KACD;IAED,IAAI,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE5C,IAAI,SAAS,EAAE;QACd,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;KACnB;IAED,IAAI,GAAG,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,EACvC,GAAG,GAAG,EAAE,CAAC;IAEV,iBAAiB;IACjB,IAAI,GAAG,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,KAAK,aAAa,EAAE;QACrE,MAAM,IAAI,KAAK,CAAC,6BAA6B,GAAG,GAAG,CAAC,CAAC;KACrD;IAED,oBAAoB;IACpB,IAAI,GAAG,CAAC,YAAY,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;QACjD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;KACpD;IAED,YAAY;IACZ,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;IAEjC,GAAG,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;IAE7E,GAAG,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvE,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IAElC,kBAAkB;IAClB,GAAG,CAAC,UAAU,GAAG,CAAC;QACjB,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAClC,0BAA0B;QAC1B,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;QAElC,IAAI,MAAM,KAAK,CAAC,EAAE;YACjB,OAAO,EAAE,CAAC;SACV;QAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;YAClD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;SACtC;QAED,OAAO,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;IAC5D,CAAC,CAAC,EAAE,CAAC;IAEL,kBAAkB;IAClB,IAAI,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,8BAA8B,EAAE;QACrD,GAAG,CAAC,UAAU,GAAG,CAAC;YACjB,IAAI,IAAI,GAAG,EAAE,CAAC;YAEd,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAClC,0BAA0B;YAC1B,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YAElC,IAAI,gBAAgB,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAChD,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,CAAC,CAAC;YAEvD,IAAI,MAAM,KAAK,CAAC,EAAE;gBACjB,OAAO,IAAI,CAAC;aACZ;YAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC;aACtC;YAED,IAAI,GAAG,GAAG,MAAM,CAAC;YAEjB,OAAO,GAAG,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE;gBAC/B,IAAI,SAAS,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBACtC,GAAG,IAAI,CAAC,CAAC;gBACT,IAAI,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBACxC,GAAG,IAAI,CAAC,CAAC;gBAET,IAAI,SAAS,KAAK,CAAC,EAAE;oBACpB,iCAAiC;oBACjC,MAAM;iBACN;gBAED,IAAI,YAAY,SAAA,CAAC;gBAEjB,QAAQ,SAAS,EAAE;oBAClB,KAAK,CAAC;wBACL,YAAY,GAAG,QAAQ,CAAC;wBACxB,MAAM;oBACP,KAAK,CAAC;wBACL,YAAY,GAAG,QAAQ,CAAC;wBACxB,MAAM;oBACP,KAAK,CAAC;wBACL,YAAY,GAAG,MAAM,CAAC;wBACtB,MAAM;oBACP,KAAK,CAAC;wBACL,YAAY,GAAG,KAAK,CAAC;wBACrB,MAAM;oBACP,KAAK,CAAC;wBACL,YAAY,GAAG,YAAY,CAAC;wBAC5B,MAAM;oBACP;wBACC,YAAY,GAAG,EAAE,CAAC;wBAClB,MAAM;iBACP;gBAED,IAAI,YAAY,EAAE;oBACjB,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,GAAG,WAAW,CAAC,CAAC;iBAClE;gBAED,GAAG,IAAI,WAAW,CAAC;aACnB;YAED,OAAO;gBACN,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,gBAAgB;aACxB,CAAC;QACH,CAAC,CAAC,EAAE,CAAC;KACL;IAED,OAAO,GAAG,CAAC;AACZ,CAAC;AAED,SAAS,kBAAkB,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM;IAChF,IAAI,OAAO,GAAG,EAAE,EACf,GAAG,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAE9B,IAAI,WAAW,KAAK,SAAS,EAAE;QAC9B,WAAW,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;KAC5B;IAED,IAAI,MAAM,KAAK,SAAS,EAAE;QACzB,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC;KACjC;IAED,WAAW;IACX,GAAG,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IAE3D,cAAc;IACd,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAExB,IAAI,YAAY,CAAC,OAAO,KAAK,CAAC,EAAE;QAC/B,OAAO,GAAG,EAAE,CAAC;QAEb,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC3C,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,CAAC,EACxC,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,EAC/E,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAErF,sBAAsB;QACtB,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACxB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC;QAEvB,wBAAwB;QACxB,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACrC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAE/B,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC;KACzB;SAAM;QACN,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EACvC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EACxC,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,EAC1D,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAElE,oBAAoB;QACpB,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACjC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAE/B,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACtB,OAAO,IAAI,EAAE,CAAC,MAAM,CAAC;QAErB,sBAAsB;QACtB,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;QACxB,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC;KACvB;IAED,6BAA6B;IAC7B,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7F,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IAC7F,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAE/B,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAE7D,2BAA2B;IAC3B,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACjG,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACjG,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAE/B,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAE/D,kCAAkC;IAClC,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACvG,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IACvG,GAAG,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IAE/B,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAElE,IAAI,YAAY,CAAC,OAAO,KAAK,CAAC,EAAE;QAC/B,6BAA6B;QAC7B,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzB,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzB,GAAG,CAAC,aAAa,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAEzB,OAAO;QACP,GAAG,CAAC,aAAa,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;KAC1C;IAED,OAAO,OAAO,GAAG,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,CAAC,OAAO,GAAG;IAChB,kBAAkB,oBAAA;IAClB,kBAAkB,oBAAA;IAClB,kBAAkB,oBAAA;CAClB,CAAC"}