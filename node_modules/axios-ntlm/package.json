{"name": "axios-ntlm", "version": "1.4.4", "description": "An NTLM auth extension to the Axios HTTP library", "main": "lib/ntlmClient.js", "types": "lib/ntlmClient.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc"}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/catbuttes/axios-ntlm.git"}, "bugs": {"url": "https://github.com/catbuttes/axios-ntlm/issues"}, "keywords": ["axios", "ntlm", "authentication", "windows authentication", "windows", "auth"], "homepage": "https://buttes.dev/axios-ntlm/", "devDependencies": {"@types/node": "^18.19.81", "typescript": "^4.9.5"}, "dependencies": {"axios": "^1.8.4", "des.js": "^1.1.0", "dev-null": "^0.1.1", "js-md4": "^0.3.2"}}