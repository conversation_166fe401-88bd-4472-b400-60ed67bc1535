<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Class: Md4</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Class: Md4</h1>

    




<section>

<header>
    
        <h2>Md4</h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

    

    <h4 class="name" id="Md4"><span class="type-signature"></span>new Md4<span class="signature">()</span><span class="type-signature"></span></h4>

    



<div class="description">
    This is internal class.
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line132">line 132</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.create">md4.create</a></li>
        </ul>
    </dd>
    

    
</dl>
















    
    </div>

    

    

    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="array"><span class="type-signature"></span>array<span class="signature">()</span><span class="type-signature"> &rarr; {Array}</span></h4>

    



<div class="description">
    Output hash as bytes array
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line498">line 498</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.array">md4.array</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Bytes array
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.array();</code></pre>



        
            

    

    <h4 class="name" id="arrayBuffer"><span class="type-signature"></span>arrayBuffer<span class="signature">()</span><span class="type-signature"> &rarr; {ArrayBuffer}</span></h4>

    



<div class="description">
    Output hash as ArrayBuffer
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line510">line 510</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li>md4.arrayBuffer</li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    ArrayBuffer
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">ArrayBuffer</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.arrayBuffer();</code></pre>



        
            

    

    <h4 class="name" id="buffer"><span class="type-signature"></span>buffer<span class="signature">()</span><span class="type-signature"> &rarr; {ArrayBuffer}</span></h4>

    



<div class="description">
    Output hash as ArrayBuffer
</div>













<dl class="details">

    

    

    

    

    

    

    

    
        <dt class="important tag-deprecated">Deprecated:</dt><dd><ul class="dummy"><li>This maybe confuse with Buffer in node.js. Please use arrayBuffer instead.</li></ul></dd>
    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line532">line 532</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.buffer">md4.buffer</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    ArrayBuffer
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">ArrayBuffer</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.buffer();</code></pre>



        
            

    

    <h4 class="name" id="digest"><span class="type-signature"></span>digest<span class="signature">()</span><span class="type-signature"> &rarr; {Array}</span></h4>

    



<div class="description">
    Output hash as bytes array
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line476">line 476</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.digest">md4.digest</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Bytes array
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.digest();</code></pre>



        
            

    

    <h4 class="name" id="hex"><span class="type-signature"></span>hex<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    



<div class="description">
    Output hash as hex string
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line431">line 431</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.hex">md4.hex</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Hex string
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.hex();</code></pre>



        
            

    

    <h4 class="name" id="toString"><span class="type-signature"></span>toString<span class="signature">()</span><span class="type-signature"> &rarr; {String}</span></h4>

    



<div class="description">
    Output hash as hex string
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line464">line 464</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.hex">md4.hex</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Hex string
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>hash.toString();</code></pre>



        
            

    

    <h4 class="name" id="update"><span class="type-signature"></span>update<span class="signature">(message)</span><span class="type-signature"> &rarr; {<a href="Md4_.html">Md4</a>}</span></h4>

    



<div class="description">
    Update hash
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line160">line 160</a>
    </li></ul></dd>
    

    

    
    <dt class="tag-see">See:</dt>
    <dd class="tag-see">
        <ul>
            <li><a href="md4.html#.update">md4.update</a></li>
        </ul>
    </dd>
    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    MD4 object.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="Md4_.html">Md4</a></span>


    </dd>
</dl>

    



        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="Md4_.html">Md4</a></li></ul><h3>Namespaces</h3><ul><li><a href="md4.html">md4</a></li></ul><h3>Global</h3><ul><li><a href="global.html#md4%2508">md4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.3</a> on Tue Jan 24 2017 15:15:12 GMT+0800 (CST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>