<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Namespace: md4</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Namespace: md4</h1>

    




<section>

<header>
    
        <h2>md4</h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        
            <div class="description"><a href="https://github.com/emn178/js-md4">js-md4</a></div>
        

        


<dl class="details">

    
    <dt class="tag-version">Version:</dt>
    <dd class="tag-version"><ul class="dummy"><li>0.3.2</li></ul></dd>
    

    

    

    

    

    

    

    

    
    <dt class="tag-author">Author:</dt>
    <dd class="tag-author">
        <ul>
            <li>Yi-Cyuan Chen [<EMAIL>]</li>
        </ul>
    </dd>
    

    
    <dt class="tag-copyright">Copyright:</dt>
    <dd class="tag-copyright"><ul class="dummy"><li>Yi-Cyuan Chen 2015-2027</li></ul></dd>
    

    
    <dt class="tag-license">License:</dt>
    <dd class="tag-license"><ul class="dummy"><li>MIT</li></ul></dd>
    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line1">line 1</a>
    </li></ul></dd>
    

    

    

    
</dl>


        
    
    </div>

    

    

    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id=".array"><span class="type-signature">(static) </span>array<span class="signature">(message)</span><span class="type-signature"> &rarr; {Array}</span></h4>

    



<div class="description">
    Output hash as bytes array
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line54">line 54</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Bytes array
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>md4.array('The quick brown fox jumps over the lazy dog');</code></pre>



        
            

    

    <h4 class="name" id=".buffer"><span class="type-signature">(static) </span>buffer<span class="signature">(message)</span><span class="type-signature"> &rarr; {ArrayBuffer}</span></h4>

    



<div class="description">
    Output hash as ArrayBuffer
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line63">line 63</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    ArrayBuffer
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">ArrayBuffer</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>md4.buffer('The quick brown fox jumps over the lazy dog');</code></pre>



        
            

    

    <h4 class="name" id=".create"><span class="type-signature">(static) </span>create<span class="signature">()</span><span class="type-signature"> &rarr; {<a href="Md4_.html">Md4</a>}</span></h4>

    



<div class="description">
    Create Md4 object
</div>













<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line78">line 78</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    MD4 object.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="Md4_.html">Md4</a></span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>var hash = md4.create();</code></pre>



        
            

    

    <h4 class="name" id=".digest"><span class="type-signature">(static) </span>digest<span class="signature">(message)</span><span class="type-signature"> &rarr; {Array}</span></h4>

    



<div class="description">
    Output hash as bytes array
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line45">line 45</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Bytes array
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">Array</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>md4.digest('The quick brown fox jumps over the lazy dog');</code></pre>



        
            

    

    <h4 class="name" id=".hex"><span class="type-signature">(static) </span>hex<span class="signature">(message)</span><span class="type-signature"> &rarr; {String}</span></h4>

    



<div class="description">
    Output hash as hex string
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line34">line 34</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    Hex string
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>md4.hex('The quick brown fox jumps over the lazy dog');
// equal to
md4('The quick brown fox jumps over the lazy dog');</code></pre>



        
            

    

    <h4 class="name" id=".update"><span class="type-signature">(static) </span>update<span class="signature">(message)</span><span class="type-signature"> &rarr; {<a href="Md4_.html">Md4</a>}</span></h4>

    



<div class="description">
    Create and update Md4 object
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line86">line 86</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    MD4 object.
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type"><a href="Md4_.html">Md4</a></span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>var hash = md4.update('The quick brown fox jumps over the lazy dog');
// equal to
var hash = md4.create();
hash.update('The quick brown fox jumps over the lazy dog');</code></pre>



        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="Md4_.html">Md4</a></li></ul><h3>Namespaces</h3><ul><li><a href="md4.html">md4</a></li></ul><h3>Global</h3><ul><li><a href="global.html#md4%2508">md4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.3</a> on Tue Jan 24 2017 15:15:12 GMT+0800 (CST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>