<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Home</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Home</h1>

    



    


    <h3> </h3>










    




    <section>
        <article><h1>js-md4</h1><p><a href="https://travis-ci.org/emn178/js-md4"><img src="https://travis-ci.org/emn178/js-md4.svg?branch=master" alt="Build Status"></a>
<a href="https://coveralls.io/r/emn178/js-md4?branch=master"><img src="https://coveralls.io/repos/emn178/js-md4/badge.svg?branch=master" alt="Coverage Status"></a><br><a href="https://nodei.co/npm/js-md4/"><img src="https://nodei.co/npm/js-md4.png?stars&amp;downloads" alt="NPM"></a><br>A simple MD4 hash function for JavaScript supports UTF-8 encoding.</p>
<h2>Demo</h2><p><a href="http://emn178.github.io/online-tools/md4.html">MD4 Online</a>  </p>
<h2>Download</h2><p><a href="https://raw.github.com/emn178/js-md4/master/build/md4.min.js">Compress</a><br><a href="https://raw.github.com/emn178/js-md4/master/src/md4.js">Uncompress</a></p>
<h2>Installation</h2><p>You can also install js-md4 by using Bower.</p>
<pre class="prettyprint source"><code>bower install js-md4</code></pre><p>For node.js, you can use this command to install:</p>
<pre class="prettyprint source"><code>npm install js-md4</code></pre><h2>Notice</h2><p><code>buffer</code> method is deprecated. This maybe confuse with Buffer in node.js. Please use <code>arrayBuffer</code> instead.</p>
<h2>Usage</h2><p>You could use like this:</p>
<pre class="prettyprint source lang-JavaScript"><code>md4('Message to hash');

var hash = md4.create();
hash.update('Message to hash');
hash.hex();

var hash2 = md4.update('Message to hash');
hash2.update('Message2 to hash');
hash2.array();</code></pre><p>If you use node.js, you should require the module first:</p>
<pre class="prettyprint source lang-JavaScript"><code>var md4 = require('js-md4');</code></pre><p>It supports AMD:</p>
<pre class="prettyprint source lang-JavaScript"><code>require(['your/path/md4.js'], function (md4) {
// ...
});</code></pre><p><a href="https://emn178.github.com/js-md4/doc/">See document</a></p>
<h2>Example</h2><pre class="prettyprint source lang-JavaScript"><code>md4(''); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4('The quick brown fox jumps over the lazy dog'); // 1bee69a46ba811185c194762abaeae90
md4('The quick brown fox jumps over the lazy dog.'); // 2812c6c7136898c51f6f6739ad08750e

// It also supports UTF-8 encoding
md4('中文'); // 223088bf7bd45a16436b15360c5fc5a0

// It also supports byte `Array`, `Uint8Array`, `ArrayBuffer`
md4([]); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4(new Uint8Array([])); // 31d6cfe0d16ae931b73c59d7e0c089c0

// Different output
md4(''); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4.hex(''); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4.array(''); // [49, 214, 207, 224, 209, 106, 233, 49, 183, 60, 89, 215, 224, 192, 137, 192]
md4.digest(''); // [49, 214, 207, 224, 209, 106, 233, 49, 183, 60, 89, 215, 224, 192, 137, 192]
md4.arrayBuffer(''); // ArrayBuffer</code></pre><h2>License</h2><p>The project is released under the <a href="http://www.opensource.org/licenses/MIT">MIT license</a>.</p>
<h2>Contact</h2><p>The project's website is located at https://github.com/emn178/js-md4<br>Author: Chen, Yi-Cyuan (<EMAIL>)</p></article>
    </section>






</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="Md4_.html">Md4</a></li></ul><h3>Namespaces</h3><ul><li><a href="md4.html">md4</a></li></ul><h3>Global</h3><ul><li><a href="global.html#md4%2508">md4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.3</a> on Tue Jan 24 2017 15:15:12 GMT+0800 (CST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>