<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>JSDoc: Global</title>

    <script src="scripts/prettify/prettify.js"> </script>
    <script src="scripts/prettify/lang-css.js"> </script>
    <!--[if lt IE 9]>
      <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <link type="text/css" rel="stylesheet" href="styles/prettify-tomorrow.css">
    <link type="text/css" rel="stylesheet" href="styles/jsdoc-default.css">
</head>

<body>

<div id="main">

    <h1 class="page-title">Global</h1>

    




<section>

<header>
    
        <h2></h2>
        
    
</header>

<article>
    <div class="container-overview">
    
        

        


<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</dl>


        
    
    </div>

    

    

    

     

    

    

    
        <h3 class="subsection-title">Methods</h3>

        
            

    

    <h4 class="name" id="md4%08"><span class="type-signature"></span>md4<span class="signature">(message)</span><span class="type-signature"> &rarr; {String}</span></h4>

    



<div class="description">
    MD4 hash function, export to global in browsers.
</div>









    <h5>Parameters:</h5>
    

<table class="params">
    <thead>
    <tr>
        
        <th>Name</th>
        

        <th>Type</th>

        

        

        <th class="last">Description</th>
    </tr>
    </thead>

    <tbody>
    

        <tr>
            
                <td class="name"><code>message</code></td>
            

            <td class="type">
            
                
<span class="param-type">String</span>
|

<span class="param-type">Array</span>
|

<span class="param-type">Uint8Array</span>
|

<span class="param-type">ArrayBuffer</span>


            
            </td>

            

            

            <td class="description last">message to hash</td>
        </tr>

    
    </tbody>
</table>






<dl class="details">

    

    

    

    

    

    

    

    

    

    

    

    

    
    <dt class="tag-source">Source:</dt>
    <dd class="tag-source"><ul class="dummy"><li>
        <a href="md4.js.html">md4.js</a>, <a href="md4.js.html#line550">line 550</a>
    </li></ul></dd>
    

    

    

    
</dl>













<h5>Returns:</h5>

        
<div class="param-desc">
    md4 hashes
</div>



<dl>
    <dt>
        Type
    </dt>
    <dd>
        
<span class="param-type">String</span>


    </dd>
</dl>

    


    <h5>Example</h5>
    
    <pre class="prettyprint"><code>md4(''); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4('The quick brown fox jumps over the lazy dog'); // 1bee69a46ba811185c194762abaeae90
md4('The quick brown fox jumps over the lazy dog.'); // 2812c6c7136898c51f6f6739ad08750e

// It also supports UTF-8 encoding
md4('中文'); // 223088bf7bd45a16436b15360c5fc5a0

// It also supports byte `Array`, `Uint8Array`, `ArrayBuffer`
md4([]); // 31d6cfe0d16ae931b73c59d7e0c089c0
md4(new Uint8Array([])); // 31d6cfe0d16ae931b73c59d7e0c089c0</code></pre>



        
    

    

    
</article>

</section>




</div>

<nav>
    <h2><a href="index.html">Home</a></h2><h3>Classes</h3><ul><li><a href="Md4_.html">Md4</a></li></ul><h3>Namespaces</h3><ul><li><a href="md4.html">md4</a></li></ul><h3>Global</h3><ul><li><a href="global.html#md4%2508">md4</a></li></ul>
</nav>

<br class="clear">

<footer>
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.4.3</a> on Tue Jan 24 2017 15:15:12 GMT+0800 (CST)
</footer>

<script> prettyPrint(); </script>
<script src="scripts/linenumber.js"> </script>
</body>
</html>