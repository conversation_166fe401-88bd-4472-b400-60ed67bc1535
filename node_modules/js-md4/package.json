{"name": "js-md4", "version": "0.3.2", "description": "A simple MD4 hash function for JavaScript supports UTF-8 encoding.", "main": "src/md4.js", "devDependencies": {"expect.js": "~0.3.1", "jscoverage": "~0.5.9", "jsdoc": "^3.4.0", "mocha": "~2.3.4", "uglifyjs": "~2.4.10"}, "scripts": {"test": "mocha tests/node-test.js -r jscoverage", "report": "mocha tests/node-test.js -r jscoverage --covout=html", "coveralls": "mocha tests/node-test.js -R mocha-lcov-reporter -r jscoverage | coveralls", "doc": "rm -rf doc;jsdoc src README.md -d doc", "compress": "uglifyjs src/md4.js --compress --mangle --comments --output build/md4.min.js", "build": "npm run-script compress;npm run-script doc"}, "repository": {"type": "git", "url": "https://github.com/emn178/js-md4.git"}, "keywords": ["md4", "hash", "encryption", "cryptography", "HMAC"], "license": "MIT", "author": "<PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "homepage": "https://github.com/emn178/js-md4", "bugs": {"url": "https://github.com/emn178/js-md4/issues"}}