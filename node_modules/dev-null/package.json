{"name": "dev-null", "version": "0.1.1", "description": "/dev/null for node streams", "main": "index.js", "scripts": {"test-main": "tap test/*.js", "test-0.10": "nave use 0.10 npm run test-main", "test-all": "npm run test-main && npm run test-0.10", "test": "if [ -e $TRAVIS ]; then npm run test-all; else npm run test-main; fi"}, "repository": {"type": "git", "url": "git://github.com/thlorenz/dev-null.git"}, "homepage": "https://github.com/thlorenz/dev-null", "dependencies": {}, "devDependencies": {"nave": "~0.4.3", "tap": "~0.4.3", "tap-stream": "~0.2.0"}, "keywords": ["streams", "test", "debug", "ignore", "silence"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": {"type": "MIT", "url": "https://github.com/thlorenz/dev-null/blob/master/LICENSE"}, "engine": {"node": ">=0.10"}}