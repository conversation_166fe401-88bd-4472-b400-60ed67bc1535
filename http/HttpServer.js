"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpServer = void 0;
const express_1 = __importDefault(require("express"));
const body_parser_1 = __importDefault(require("body-parser"));
const http = __importStar(require("http"));
const ConfigUtil_1 = require("../util/ConfigUtil");
const MyLogger_1 = require("../util/MyLogger");
class HttpServer {
    app_;
    server_;
    constructor() {
        this.app_ = (0, express_1.default)();
        this.app_.use(body_parser_1.default.json({ limit: '10mb' }));
        this.app_.use(body_parser_1.default.urlencoded({ extended: false }));
        this.app_.use(function (error, req, res, next) {
            if (typeof (error) === "number")
                res.sendStatus(error);
            else {
                console.error("[HTTP API]异常", error.toString());
                res.sendStatus(500);
            }
            next();
        });
        this.server_ = http.createServer(this.app_);
    }
    get server() {
        return this.server_;
    }
    BindingRouter(router) {
        this.app_.use('/api/countryInterface', router);
    }
    Startup() {
        this.server_.listen(ConfigUtil_1.ConfigUtil.port);
        MyLogger_1.MyLogger.log(`http api run on localhost ${ConfigUtil_1.ConfigUtil.port}`);
    }
}
exports.HttpServer = HttpServer;
