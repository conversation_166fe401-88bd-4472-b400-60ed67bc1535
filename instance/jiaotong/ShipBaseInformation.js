"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const ConfigUtil_1 = require("../../util/ConfigUtil");
const MyLogger_1 = require("../../util/MyLogger");
class ShipBaseInformation {
    shipBaseInformationSecret_;
    authenticationToken_;
    constructor(shipBaseInformationSecret_, authenticationToken_) {
        this.shipBaseInformationSecret_ = shipBaseInformationSecret_;
        this.authenticationToken_ = authenticationToken_;
    }
    async loadData(condition) {
        try {
            let data = {
                sid: this.shipBaseInformationSecret_.requestParams.apiSid,
                condition: condition
            };
            let header = {
                gjzwfwpt_rid: this.shipBaseInformationSecret_.requestParams.rid,
                gjzwfwpt_sid: this.shipBaseInformationSecret_.requestParams.sid,
                gjzwfwpt_rtime: this.shipBaseInformationSecret_.signTime,
                gjzwfwpt_sign: this.shipBaseInformationSecret_.sign,
                access_token: this.authenticationToken_.token,
            };
            console.log('船舶基本信息接口请求头---》', header);
            console.log('船舶基本信息接口请求参数---》', data);
            let { data: result } = await axios_1.default.post(ConfigUtil_1.ConfigUtil.interfaceUrl + '/gateway/httpproxy', data, {
                headers: header
            });
            MyLogger_1.MyLogger.log('船舶基本信息接口返回结果', JSON.stringify(result));
            if (result && result.stateCode === '0000') {
                return result.data;
            }
            else {
                MyLogger_1.MyLogger.log('船舶基本信息接口返回结果 异常', result);
            }
            return undefined;
        }
        catch (e) {
            MyLogger_1.MyLogger.log('交通部_船舶基本信息接口调用失败', e);
        }
    }
}
exports.default = ShipBaseInformation;
