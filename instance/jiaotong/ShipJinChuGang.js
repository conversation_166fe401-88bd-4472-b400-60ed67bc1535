"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const axios_1 = __importDefault(require("axios"));
const ConfigUtil_1 = require("../../util/ConfigUtil");
const MyLogger_1 = require("../../util/MyLogger");
class ShipJinChuGang {
    jinchugangSecret_;
    authenticationToken_;
    constructor(jinchugangSecret_, authenticationToken_) {
        this.jinchugangSecret_ = jinchugangSecret_;
        this.authenticationToken_ = authenticationToken_;
    }
    /**
     * 调用频率限制    2000次/天，每分钟50次
     * 数据范围    2018年
     * 超时时间(秒)    4秒
     * 目前参数只支持SHIP_MMSI
     */
    /*public async loadData(params: ShipJinChuGangParams | any): Promise<ShipJinChuGangData[] | undefined> {
    let keys = Object.keys(params);
            let condition: { dataCode: string, dataValue: string }[] = [];
            keys.forEach((k) => {
                condition.push({
                    dataCode: k,
                    dataValue: params[k]
                });
            });*/
    async loadData(condition) {
        try {
            let data = {
                sid: this.jinchugangSecret_.requestParams.apiSid,
                condition: condition
            };
            let header = {
                gjzwfwpt_rid: this.jinchugangSecret_.requestParams.rid,
                gjzwfwpt_sid: this.jinchugangSecret_.requestParams.sid,
                gjzwfwpt_rtime: this.jinchugangSecret_.signTime,
                gjzwfwpt_sign: this.jinchugangSecret_.sign,
                access_token: this.authenticationToken_.token,
            };
            MyLogger_1.MyLogger.log('船舶进出港接口请求头---》', JSON.stringify(header));
            MyLogger_1.MyLogger.log('船舶进出港接口请求参数---》', JSON.stringify(data));
            let { data: result } = await axios_1.default.post(ConfigUtil_1.ConfigUtil.interfaceUrl + '/gateway/httpproxy', data, {
                headers: header
            });
            MyLogger_1.MyLogger.log('船舶进出港接口返回结果', JSON.stringify(result));
            if (result && result.stateCode === '0000') {
                return result.data;
            }
            else {
                MyLogger_1.MyLogger.log('船舶进出港接口返回结果 异常', result);
            }
            return undefined;
        }
        catch (e) {
            MyLogger_1.MyLogger.log('交通部_船舶进出港报告接口调用失败', e);
        }
    }
}
exports.default = ShipJinChuGang;
